<template>
  <div class="homePieDiagram">
    <div class="pie-wrap">
      <div class="pie-item">
        <p>预约报到/现场登记人数对比：</p>
        <div class="progressDiv">
          <div id="peoRatio" class="sexRatio-chart"></div>
          <div class="textDiv">
            <div class="male">
              <span class="spanText">
                <span class="gray register"> 预约报到人数：</span>
                <span class="block">
                  {{ activedBookedLocalInfo.bookedActivedNum }}人
                </span>
              </span>
              <span class="spanText">
                <span class="gray register"> 现场报到人数：</span>
                <span class="block">
                  {{ activedBookedLocalInfo.localActivedNum }}人
                </span>
              </span>
              <span class="spanText">
                <span class="gray register">报道总人数：</span>
                <span class="block">
                  {{ activedBookedLocalInfo.activedNum }}人
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="pie-item">
        <p>各个分类的人数对比：</p>
        <div class="progressDiv">
          <div id="numRatio2" class="sexRatio-chart"></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import * as echarts from 'echarts';
require('echarts/theme/macarons');
export default {
  name: 'homePieDiagram',
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    activedBookedLocalInfo: {
      type: Object,
      default: { bookedActivedNum: 0, localActivedNum: 0, activedNum: 0 }
    }
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    //获取扇形数据
    getPieDta(pieData1, pieData2) {
      this.$nextTick(() => {
        this.pieDiagram(pieData1);
        this.pieDiagram2(pieData2);
      });
    },
    //预约报到/现场登记人数对比
    pieDiagram(pieData1) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById('peoRatio')
      );
      //如果为空 则正常进行渲染 反之 不再进行初始化
      if (myChart == null) {
        myChart = echarts.init(document.getElementById('peoRatio'));
      }
      myChart.setOption({
        tooltip: {
          trigger: 'item',
          confine: true
        },
        color: ['#1770DF', '#FAB63B'],
        series: [
          {
            name: '预约报到/现场登记人数对比：',
            type: 'pie',
            radius: 56,
            label: {
              fontSize: 14,
              formatter: '{b}\n{c} 人',
              minMargin: 5,
              edgeDistance: 10,
              lineHeight: 20,
              color: 'inherit'
            },
            labelLayout: function (params) {
              const isLeft = params.labelRect.x < myChart.getWidth() / 2;
              const points = params.labelLinePoints;
              // Update the end point.
              points[2][0] = isLeft
                ? params.labelRect.x
                : params.labelRect.x + params.labelRect.width;
              return {
                labelLinePoints: points
              };
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 4
            },
            data: [
              { value: pieData1.bookedActivedNum, name: '预约报到' },
              { value: pieData1.localActivedNum, name: '现场报到' }
            ]
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    },
    //各个分类的人数对比
    pieDiagram2(pieData2) {
      let myChart = echarts.getInstanceByDom(
        document.getElementById('numRatio2')
      );
      //如果为空 则正常进行渲染 反之 不再进行初始化
      if (myChart == null) {
        myChart = echarts.init(document.getElementById('numRatio2'));
      }
      myChart.setOption({
        tooltip: {
          trigger: 'item'
        },
        title: {
          x: 'center', //水平安放位置，默认为'left'，可选为：'center' | 'left' | 'right' | {number}（x坐标，单位px）
          y: 'bottom', //垂直安放位置，默认为top，可选为：'top' | 'bottom' | 'center' | {number}（y坐标，单位px）
          left: '25%',
          top: '80%',
          textAlign: 'center' // 标题始终和图表保持居中
        },
        legend: {
          itemWidth: 12,
          itemHeight: 12,
          icon: 'circle',
          x: 'right',
          y: 'center',
          orient: 'vertical', //设置图例排列纵向显示
          align: 'left', //设置图例中文字位置在icon标识符的右侧
          left: '36%',
          top: '0',
          itemGap: 10, //设置图例之间的间距
          padding: [0, 10, 0, 0], //设置图例与圆环图之间的间距
          formatter: function (name) {
            var value;
            pieData2.activedPeClses.forEach((item) => {
              if (item.name === name) {
                value = item.value;
              }
            });
            return `{a|${name}}{b|${value}人}`;
          },
          // 自定义图例文字样式
          textStyle: {
            rich: {
              a: {
                color: 'rgba(45, 52, 54, 0.6)',
                padding: [0, 10, 0, 0],
                fontSize: 14
              },
              b: {
                color: '#4A4A4A',
                padding: [0, 10, 0, 0],
                fontSize: 18
              }
            }
          }
        },
        graphic: [
          // 设置圆环内文字始终在圆环的正中间
          {
            type: 'group',
            left: '18%',
            top: '42%',
            bounding: 'raw',
            children: [
              {
                type: 'text',
                style: {
                  text: '报到总数',
                  fontSize: 14,
                  fill: '#9C9C9C', //文字的颜色
                  textVerticalAlign: 'middle',
                  textAlign: 'center'
                }
              }
            ]
          },
          {
            type: 'group',
            left: '18%',
            top: '56%',
            bounding: 'raw',
            children: [
              {
                type: 'text',
                style: {
                  text: pieData2.totalActived + '人',
                  fontSize: 14,
                  fill: '#9C9C9C', //文字的颜色
                  textVerticalAlign: 'middle',
                  textAlign: 'center'
                }
              }
            ]
          }
        ],
        color: [
          '#D63031',
          '#fb7435',
          '#FAB63B',
          '#F9D914',
          '#65e069',
          '#3CB34F',
          '#01a1e7',
          '#1770DF',
          '#7364F4',
          '#e15eab'
        ], //环形颜色
        series: [
          {
            name: '各个分类的人数对比',
            type: 'pie',
            radius: ['54%', '76%'], //饼图的半径，数组的第一项是内半径，第二项是外半径。支持设置成百分比，相对于容器高宽中较小的一项的一半。可以将内半径设大显示成圆环图, 设置内半径和外半径，形成环状
            center: ['18%', '50%'], //饼图的中心(圆心)坐标，数组的第一项是横坐标，第二项是纵坐标。支持设置成百分比，设置成百分比时第一项是相对于容器宽度，第二项是相对于容器高度。这里的center一旦设置, 则 graphic/legend 里的内容位置也要相应进行调整
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 2
            },
            data: pieData2.activedPeClses
          }
        ]
      });
      // 浏览器大小调整echarts自适应
      window.addEventListener('resize', () => {
        if (myChart) {
          myChart.resize();
        }
      });
    }
  }
};
</script>

<style lang="less" scoped>
.homePieDiagram {
  display: inline-block;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: row;
  .progress-box {
    flex: 1;
    display: flex;
    align-items: flex-end;
    padding: 18px;
    color: #2d3436;
    font-family: PingFangSC-Regular;
    background: #fff;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
  }
  .progress-info {
    flex: 1;
  }
  .title {
    opacity: 0.6;
    font-size: 14px;
    margin-bottom: 10px;
  }
  .number {
    font-size: 42px;
    span {
      font-size: 14px;
      margin-left: 10px;
    }
  }
  .pie-wrap {
    display: flex;
    flex-direction: row;
    width: 100%;
  }
  .pie-item {
    background: #fff;
    border-radius: 4px;
    padding: 10px;
    margin-right: 10px;
    flex: 1;
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.12),
      0 0 6px rgba(0, 0, 0, 0.04);

    &:last-child {
      margin-right: 0;
    }
    span {
      min-width: 140px;
    }
    .progressDiv {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
    }
  }
  .sexRatio-chart {
    width: 100%;
    height: 176px;
  }
  .pie-text {
    display: flex;
    justify-content: center;
    align-items: center;
    span {
      font-weight: normal;
    }
  }
  .male {
    color: #4a95f4;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    .spanText {
      padding-bottom: 8px;
      &:nth-child(1) {
        .register {
          display: flex;
          align-items: center;
          &::before {
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
            background: #1770df;
          }
        }
      }
      &:nth-child(2) {
        .register {
          display: flex;
          align-items: center;
          &::before {
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
            background: #fab63b;
          }
        }
      }
      &:nth-child(3) {
        .register {
          display: flex;
          align-items: center;
          &::before {
            content: '';
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
            background: #000;
          }
        }
      }
    }
  }
  .female {
    color: #ff8384;
    font-size: 14px;
    display: flex;
    flex-direction: row;
    .spanText {
      padding-bottom: 8px;
    }
  }
  .icon-text {
    margin-right: 20px;
  }
  /deep/.el-skeleton {
    display: flex;
  }
  .classify-chart {
    width: 100%;
    // height: 200px;
  }
  .gray {
    font-family: PingFangSC-Regular;
    font-size: 14px;
    color: rgba(45, 52, 54, 0.6);
  }
  .block {
    font-family: PingFangSC-Medium;
    font-size: 18px;
    color: #2d3436;
    opacity: 1;
  }
  #peoRatio,
  #numRatio2,
  .textDiv {
    margin: auto;
  }
}
</style>
