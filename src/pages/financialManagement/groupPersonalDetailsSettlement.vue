<template>
  <!-- 团体个人明细结算 -->
  <div class="groupPersonalDetailsSettlement">
    <div class="groupPersonalDetailsSettlement-wrap">
      <div class="search-operate">
        <div class="operate-item">
          <span style="width: 70px">登记时间</span>
          <el-date-picker
            :picker-options="{ shortcuts: G_datePickerShortcuts }"
            v-model.trim="searchInfo.date"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :clearable="false"
            size="small"
            class="select"
          >
          </el-date-picker>
        </div>
        <div class="operate-item">
          <span>体检单位</span>
          <el-cascader
            ref="cascader_ref"
            v-model="casValue"
            :options="options"
            :filter-method="filterMethod"
            :props="{ multiple: true }"
            clearable
            filterable
            size="small"
            collapse-tags
            class="select"
            @change="getChecked"
          >
          </el-cascader>
        </div>
        <div class="operate-item">
          <span>单位部门</span>
          <el-select
            placeholder="请选择"
            size="small"
            filterable
            clearable
            v-model="searchInfo.deptCode"
            class="select"
            :disabled="!searchInfo.companyCode"
          >
            <el-option
              v-for="item in deptCodeList"
              :key="item.deptCode"
              :label="item.deptName"
              :value="item.deptCode"
            >
            </el-option>
          </el-select>
        </div>
        <div class="operate-item">
          <el-checkbox v-model="searchInfo.checked">已体检</el-checkbox>
        </div>
        <div class="operate-item">
          <BtnCommon :btnList="['查询', '打印']" @search="searchClick">
            <template slot="footAdd">
              <el-button
                size="small"
                class="yellow_btn btn"
                @click="exports"
                icon="iconfont icon-daochu"
                >导出</el-button
              >
            </template>
          </BtnCommon>
        </div>
      </div>
      <div class="legend">
        <span>实检人数：{{ infoList.checkCount }}人</span>
        <span>套餐费用：{{ infoList.totalClusFee }}元</span>
        <span>加查费用：{{ infoList.totalAddSearchFee }}元</span>
        <span>未查费用：{{ infoList.totalUnSearchedFee }}元</span>
        <span>实收费用：{{ infoList.totalActualFee }}元</span>
      </div>
      <div class="table-wrap">
        <div class="table">
          <PublicTable
            :viewTableList.sync="tableData"
            :theads.sync="theads"
            :isSortShow="false"
            :columnWidth="columnWidth"
          >
            <template #sex="{ scope }">
              {{ G_EnumList['Sex'][scope.row.sex] }}
            </template>
          </PublicTable>
        </div>
        <!-- <div class="total">
          <span>总计</span>
          <span>{{ infoList.totalPrice }}</span>
        </div> -->
      </div>
      <ul class="info-wrap">
        <!-- <li>
          <span>制表时间：{{ infoList.tabulationDate }}</span>
          <span>制表：{{ G_userInfo.codeOper.name }}</span>
        </li>
        <li>
          <span>
            1.体检日期：{{ infoList.beinDate }}至{{ infoList.endDate }}
          </span>
        </li>
        <li>
          <span>2.单位体检次数：{{ infoList.companyTimes }}</span>
        </li> -->
        <li>
          <span>1.收款单位：{{ infoList.payeeCompany }}</span>
        </li>
        <li>
          <span>2.开户银行：{{ infoList.openBank }}</span>
          <span>账号：{{ infoList.bankAccount }}</span>
        </li>
        <li>
          <span>
            3.纳税人识别号(社会信用代码)：{{ infoList.socialCreditCode }}
          </span>
        </li>
        <li>
          <span>4.地址：{{ infoList.address }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import BtnCommon from './components/btnCommon.vue';
import PublicTable from '@/components/publicTable.vue';
import { dataUtils } from '@/common';
import { mapGetters } from 'vuex';
export default {
  name: 'groupPersonalDetailsSettlement',
  components: { BtnCommon, PublicTable },
  data() {
    return {
      searchInfo: {
        date: [dataUtils.getDate(), dataUtils.getDate()],
        companyCode: '',
        companyTimes: [],
        deptCode: '',
        checked: false
      },
      companyList: [],
      options: [],
      casValue: [],
      shareScopeEnd: [],
      deptCodeList: [],
      tableData: [],
      theads: {
        deptName: '部门',
        clusName: '套餐名称',
        name: '姓名',
        regNo: '体检号',
        sex: '性别',
        clusFee: '套餐费用',
        addSearchFee: '加查费用',
        unSearchedFee: '未查费用',
        actualFee: '实收费用'
      },
      columnWidth: {
        regNo: 130,
        clusName: 130
      },
      infoList: {
        checkCount: 0,
        totalClusFee: 0,
        totalAddSearchFee: 0,
        totalUnSearchedFee: 0,
        totalActualFee: 0
      }
    };
  },
  created() {
    this.getCompanyAndTimes();
  },
  computed: {
    ...mapGetters(['G_EnumList', 'G_userInfo', 'G_datePickerShortcuts'])
  },
  methods: {
    // 获取部门
    getDeptCode() {
      if (!this.searchInfo.companyCode) return;
      let data = {
        companyCode: this.searchInfo.companyCode
      };
      this.$ajax.post(this.$apiUrls.R_CodeCompanyDepartment, data).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        this.deptCodeList = returnData || [];
      });
    },
    // 获取医院信息
    getHospitalInfo() {
      this.$ajax
        .post(this.$apiUrls.RD_CodeHospitalInfo + '/Read', '', {
          query: { hospCode: '01' }
        })
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) return;
          this.infoList = {
            ...this.infoList,
            ...returnData
          };
        });
    },
    // 获取单位和体检次数
    getCompanyAndTimes() {
      this.$ajax.post(this.$apiUrls.CompanyAndTimes).then((r) => {
        let { success, returnData } = r.data;
        if (!success) return;
        let newData = [];
        newData = returnData.map((item) => {
          return {
            value: item.companyCode,
            label: item.companyName,
            children: item.companyTimes.map((child) => {
              return {
                value: child.companyTimes,
                label: `${child.companyTimes}　${dataUtils.subBlankDate(
                  child.beginDate
                )}　${dataUtils.subBlankDate(child.endDate)}`
              };
            })
          };
        });
        this.options = newData;
      });
    },
    filterMethod(node, val) {
      return node.parent.label.includes(val) || node.parent.value.includes(val);
    },
    // 查询
    searchClick() {
      if (!this.searchInfo.companyCode) {
        this.$message({
          showClose: true,
          message: '请选择单位!',
          type: 'warning'
        });
        return;
      }
      let [beinDate, endDate] = this.searchInfo.date;
      let data = {
        beinDate: beinDate,
        endDate: endDate,
        companyCode: this.searchInfo.companyCode,
        companyTimes: this.searchInfo.companyTimes,
        deptCode: this.searchInfo.deptCode,
        checked: this.searchInfo.checked
      };
      this.$ajax
        .post(this.$apiUrls.CompanySettlementByPersonDetails, data)
        .then((r) => {
          let { success, returnData } = r.data;
          if (!success) {
            this.tableData = [];
            this.infoList = {
              checkCount: 0,
              totalClusFee: 0,
              totalAddSearchFee: 0,
              totalUnSearchedFee: 0,
              totalActualFee: 0
            };
          } else {
            this.tableData = returnData.personAccount || [];
            this.infoList = {
              totalAddItemPrice: returnData.totalAddItemPrice,
              totalPrice: returnData.totalPrice,
              totalRefuseCheckPrice: returnData.totalRefuseCheckPrice
            };
          }

          this.getHospitalInfo();
        });
    },
    // 单位勾选切换
    getChecked(val) {
      if (val.length === 0) {
        this.searchInfo.companyCode = '';
        return;
      }
      let changeFlag = false;
      let changeItem = [];
      if (this.shareScopeEnd.length == 0) {
        this.casValue = val;
      } else {
        // 与原数组比对
        this.casValue.forEach((item) => {
          if (item[0] !== this.shareScopeEnd[0][0]) {
            // 一级标签不同
            changeFlag = true;
            changeItem.push(item);
          }
        });
      }
      if (changeFlag) {
        this.casValue = [];
        this.casValue = changeItem;
      }
      this.shareScopeEnd = this.casValue;
      if (val.length !== 0) {
        this.searchInfo.companyCode = this.casValue[0][0];
        let companyTimes = [];
        this.casValue.map((item) => {
          if (item) {
            companyTimes.push(item[1]);
          }
        });
        this.searchInfo.companyTimes = companyTimes;
      }
      this.getDeptCode();
    },
    // 导出
    exports() {}
  }
};
</script>

<style lang="less" scoped>
.groupPersonalDetailsSettlement {
  color: #2d3436;
  display: flex;
  flex-direction: column;
  .select {
    width: 100%;
  }
  .groupPersonalDetailsSettlement-wrap {
    background: #fff;
    flex: 1;
    overflow: auto;
    padding: 10px;
    display: flex;
    flex-direction: column;
  }
  .search-operate {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .operate-item {
    margin-right: 10px;
    display: flex;
    align-items: center;
    span {
      margin-right: 10px;
      font-weight: 600;
      font-size: 14px;
      width: 78px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
  .legend {
    font-size: 14px;
    font-weight: 600;
    color: #1770df;
    margin-bottom: 10px;
    span {
      margin-right: 80px;
    }
  }
  .table-wrap {
    flex: 1;
    overflow: auto;
    border: 1px solid #d8dee1;
    border-radius: 4px;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
  }
  .table {
    flex: 1;
    overflow: auto;
  }
  // .price {
  //   text-align: right;
  // }
  .total {
    background: rgba(23, 112, 223, 0.2);
    padding: 10px;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
  }
  .info-wrap {
    font-size: 14px;
    font-weight: 600;
    li {
      line-height: 26px;
      span {
        margin-right: 20px;
      }
    }
  }
}
</style>
